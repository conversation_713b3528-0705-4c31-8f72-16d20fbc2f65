import { ModernDashboardLayout } from "@/components/dashboard/modern-layout";
import { CurrentSubscription } from "../../../components/billing/current-subscription";
import { SubscriptionPlans } from "../../../components/billing/subscription-plans";
import { ProductsGrid } from "../dashboard/products-grid";

export default function BillingPage() {
  return (
    <ModernDashboardLayout
      title="Billing & Subscriptions"
      subtitle="Manage your subscription plans and billing information"
    >
      <div className="space-y-8">
        {/* Current Subscription Status */}
        <CurrentSubscription />

        {/* Available Subscription Plans */}
        <SubscriptionPlans />

        {/* Products Grid for Checkout */}
        <ProductsGrid />
      </div>
    </ModernDashboardLayout>
  );
}
